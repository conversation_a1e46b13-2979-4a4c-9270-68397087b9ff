<?php

namespace App\Http\Controllers\Shuttle;

use Sina\Shuttle\Models\Component;
use Sina\Shuttle\Models\ScaffoldInterface;
use Sina\Shuttle\Models\PageComponent;
use App\Models\Conference;
use Illuminate\Http\Request;
use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Http\Resources\DataTableResource;

class ConferenceController extends ShuttleController
{
    public function addComponent(Request $request, Conference $conference)
    {
        $lang = 'en';
        $component = Component::find($request->component_id);
        // if (!$component) {
        //     return redirect()->back()->withErrors([
        //         'error' => ['Component not found.']
        //     ]);
        // }
        $conference->components()->attach([
            $component->id => ['setting' => [], 'locale' => $lang, 'model_type' => Conference::class]
        ]);

        return redirect()->back()->withErrors([
            'success' => ['Section updated']
        ]);
    }

    public function copy(Request $request, Conference $conference)
    {

    }

    public function destroyComponent(Request $request, PageComponent $component)
    {
        $component->delete();

        return redirect()->back()->withErrors([
            'success' => ['Section updated']
        ]);
    }

    public function store(ScaffoldInterface $scaffold_interface, Request $request)
    {
        $model = $this->save($scaffold_interface, $request);

        foreach ($request->locations ?? [] as $loc){
            $model->packages()->create($loc);
        }

        return redirect()->route("shuttle.scaffold_interface.index", $scaffold_interface)->with([
            'message' => __('voyager::generic.successfully_updated'),
            'alert-type' => 'success',
        ]);
    }

    public function update(ScaffoldInterface $scaffold_interface, Request $request, $id)
    {
        // Use parent update method but handle packages separately
        $dataType = $scaffold_interface;
        $model = app($dataType->model);
        $data = $model->findOrFail($id);

        $lang = $request->get('lang', config('translatable.locales')[0]);
        $translated_attr = [];
        $values = array();

        $this->validateBread($request->all(), $scaffold_interface->rows, $scaffold_interface->name, $id)->validate();

        if ($scaffold_interface->translation_model) {
            $translated_attr = app($scaffold_interface->model)->translatedAttributes;
            $values[$lang] = $request->only($translated_attr);
        }
        $translated_attr[] = '_token';
        $translated_attr[] = '_method';

        $values = array_merge($values, $request->except(app($scaffold_interface->model)->translatedAttributes));

        // Handle file uploads
        foreach ($request->allFiles() as $key => $file) {
            $values[$key] = $file->store('public/upload');
        }

        // Handle file deletions - check for all possible file fields
        $fileFields = ['image', 'cover', 'video', 'pdf', 'file'];
        foreach ($fileFields as $field) {
            if ($request->has($field) && (is_null($request->$field) || $request->$field === '')) {
                $values[$field] = null;
            }
        }

        $data->update($values);

        // Handle packages
        $ids = [];
        foreach ($request->locations ?? [] as $loc){
            $m = $data->packages()->updateOrCreate(['id' => data_get($loc, 'id')], $loc);
            $ids[] = $m->id;
        }

        $data->packages()->whereNotIn('id', $ids)->delete();

        return redirect()->route("shuttle.scaffold_interface.index", $scaffold_interface)->with([
            'message' => __('voyager::generic.successfully_updated'),
            'alert-type' => 'success',
        ]);
    }

    public function datatable(Request $request, ScaffoldInterface $scaffoldInterface)
    {
        return $this->getDataTableResource(
            DataTableResource::newInstance()
                ->setScaffoldInterface($scaffoldInterface, function ($query) use ($request){
                    if(!empty($request->q)){
                        $query = $query->where('title', 'like', '%'.$request->q.'%');
                    }

                    if(!empty($request->category_id)){
                        $query = $query->where('category_id', $request->category_id);
                    }

                    if(!empty($request->start_at)){
                        $query = $query->whereIn('id', function ($q) use ($request) {
                            $q->select('conference_id')->from('conference_packages')->where('start_at', '>=', $request->start_at);
                        });
                    }

                    if(!empty($request->end_at)){
                        $query = $query->whereIn('id', function ($q) use ($request) {
                            $q->select('conference_id')->from('conference_packages')->where('end_at', '<=', $request->end_at);
                        });
                    }

                    return $query;
                })
//                ->addAction(fn ($data) => '<a href="' . route('shuttle.conference.copy',  $data->id) . '" class="btn btn-bootstrap-padding btn-primary"><i class="glyph-icon simple-icon-pencil"></i></a>')
                ->addAction(fn ($data) => '<a href="' . route('shuttle.scaffold_interface.edit', [$scaffoldInterface, $data->id]) . '" class="btn btn-bootstrap-padding btn-primary"><i class="glyph-icon simple-icon-pencil"></i></a>')
                ->addAction(fn ($data) => '<button type="button" class="btn btn-bootstrap-padding btn-danger remove-item" data-id="'.$data->id.'"><i class="glyph-icon simple-icon-trash"></i></button>')
        )->json();
    }

}
